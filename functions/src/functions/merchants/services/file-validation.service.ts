import { logger } from "../../../helpers/logger.js";
import { VerificationFile } from "./bank-verification.service.js";

const ALLOWED_FILE_TYPES = ["image/jpeg", "image/jpg", "image/png", "application/pdf"];
const MAX_FILE_SIZE = 10 * 1024 * 1024;

export function validateVerificationFile(
  file: VerificationFile,
  requestId: string,
  entityId: string
): void {
  if (!ALLOWED_FILE_TYPES.includes(file.type.toLowerCase())) {
    logger.error("Invalid file type for bank verification", {
      requestId,
      entityId,
      fileType: file.type,
      allowedTypes: ALLOWED_FILE_TYPES,
    });
    throw new Error("Invalid file type. Only JPEG, PNG, and PDF files are allowed for bank verification.");
  }

  if (file.size > MAX_FILE_SIZE) {
    logger.error("File size exceeds limit for bank verification", {
      requestId,
      entityId,
      fileSize: file.size,
      maxFileSize: MAX_FILE_SIZE,
    });
    throw new Error("File size exceeds 10MB limit for bank verification documents.");
  }
}